// ESP32 Vending Server for GT-102 Motor Board
// Receives HTTP GET requests with slot, timestamp, and HMAC
// Sends command over Serial2 to GT-102 board

#include <WiFi.h>
#include <WebServer.h>
#include "mbedtls/md.h"

// === CONFIGURATION ===
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";
const char* hmac_secret = "your_shared_secret";
const int vendingBoardAddr = 1;

WebServer server(80);
HardwareSerial& vendingSerial = Serial2;

// === CRC16 Modbus ===
uint16_t crc16_modbus(const uint8_t* data, size_t length) {
  uint16_t crc = 0xFFFF;
  for (size_t i = 0; i < length; i++) {
    crc ^= data[i];
    for (int j = 0; j < 8; j++) {
      if (crc & 0x0001) {
        crc >>= 1;
        crc ^= 0xA001;
      } else {
        crc >>= 1;
      }
    }
  }
  return crc;
}

// === HMAC Validator ===
bool validateHMAC(String data, String hash) {
  byte result[32];
  mbedtls_md_context_t ctx;
  const mbedtls_md_info_t* info = mbedtls_md_info_from_type(MBEDTLS_MD_SHA256);

  mbedtls_md_init(&ctx);
  mbedtls_md_setup(&ctx, info, 1);
  mbedtls_md_hmac_starts(&ctx, (const unsigned char*)hmac_secret, strlen(hmac_secret));
  mbedtls_md_hmac_update(&ctx, (const unsigned char*)data.c_str(), data.length());
  mbedtls_md_hmac_finish(&ctx, result);
  mbedtls_md_free(&ctx);

  char hex[65];
  for (int i = 0; i < 32; i++) sprintf(hex + i * 2, "%02x", result[i]);

  return hash.equalsIgnoreCase(String(hex));
}

// === Send GT-102 Command ===
void sendVendCommand(uint8_t slot) {
  uint8_t frame[20] = {0};
  frame[0] = vendingBoardAddr;
  frame[1] = 0x05; // Vend command
  frame[2] = slot; // Y1 = slot
  frame[3] = 2;     // Y2 = motor type (2-wire)
  frame[4] = 0;     // Y3 = light-screen mode (0 = ignore)
  // Y4-Y6 = over/under current, timeout (0 = default)

  uint16_t crc = crc16_modbus(frame, 18);
  frame[18] = crc & 0xFF;
  frame[19] = (crc >> 8) & 0xFF;

  vendingSerial.write(frame, 20);
}

// === HTTP Handler ===
void handleVend() {
  if (!server.hasArg("slot") || !server.hasArg("ts") || !server.hasArg("hash")) {
    server.send(400, "text/plain", "Missing parameters");
    return;
  }

  String slotStr = server.arg("slot");
  String ts = server.arg("ts");
  String hash = server.arg("hash");

  // Optional: reject old timestamps (to avoid replay)
  long now = millis() / 1000;
  if (abs(now - ts.toInt()) > 30) {
    server.send(403, "text/plain", "Timestamp expired");
    return;
  }

  String data = "slot=" + slotStr + "&ts=" + ts;
  if (!validateHMAC(data, hash)) {
    server.send(403, "text/plain", "Invalid hash");
    return;
  }

  uint8_t slot = slotStr.toInt();
  sendVendCommand(slot);
  server.send(200, "text/plain", "Vend triggered");
}

void setup() {
  Serial.begin(115200);
  vendingSerial.begin(9600, SERIAL_8N1, 16, 17); // RX, TX (adjust GPIOs)

  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nWiFi connected. IP: " + WiFi.localIP().toString());

  server.on("/vend", handleVend);
  server.begin();
  Serial.println("Server started.");
}

void loop() {
  server.handleClient();
}
